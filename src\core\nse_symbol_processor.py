"""
NSE Symbol Processor for downloading, parsing, and filtering NSE symbols.
Handles NSE_CM.csv and NSE_FO.csv files with pattern-based filtering.
"""

import os
import re
import logging
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import psycopg2
from psycopg2.extras import RealDictCursor

from .symbol_downloader import EnhancedSymbolDownloader
from ..database.connection import engine
from ..core.config import settings

logger = logging.getLogger(__name__)


class NSESymbolProcessor:
    """Process NSE symbols with pattern matching and database operations."""
    
    def __init__(self, config_path: str = None):
        """Initialize the NSE symbol processor."""
        self.config_path = config_path or "config.yaml"
        self.downloader = EnhancedSymbolDownloader(config_path)
        self.db_engine = engine
        
        # Symbol patterns for filtering
        self.patterns = {
            'EQUITY': re.compile(r'^[A-Z0-9&]+\-EQ$'),
            'INDEX': re.compile(r'^[A-Z0-9]+\-INDEX$'),
            'FUTURES': re.compile(r'^[A-Z0-9]+\d{2}[A-Z]{3}FUT$'),
            'OPTIONS_MONTHLY': re.compile(r'^[A-Z0-9]+\d{2}[A-Z]{3}\d+(?:\.\d+)?(?:CE|PE)$'),
            'OPTIONS_WEEKLY': re.compile(r'^[A-Z0-9]+\d{2}\d\d{2}\d+(?:\.\d+)?(?:CE|PE)$')
        }
        
        # NSE_CM.csv column mapping (no headers in file)
        self.nse_cm_columns = [
            'fytoken', 'company_name', 'segment', 'lot_size', 'tick_size',
            'isin', 'trading_hours', 'last_update_date', 'expiry_timestamp',
            'symbol_name', 'exchange_segment', 'exchange_instrument_id',
            'instrument_id', 'symbol', 'token', 'minimum_lot_size',
            'instrument_type', 'underlying_fytoken', 'underlying_symbol',
            'option_type', 'strike_price'
        ]
        
        # NSE_FO.csv column mapping (no headers in file)
        self.nse_fo_columns = [
            'fytoken', 'company_name', 'segment', 'lot_size', 'tick_size',
            'isin', 'trading_hours', 'last_update_date', 'expiry_timestamp',
            'symbol_name', 'exchange_segment', 'exchange_instrument_id',
            'instrument_id', 'symbol', 'token', 'minimum_lot_size',
            'instrument_type', 'underlying_fytoken', 'underlying_symbol',
            'option_type', 'strike_price'
        ]
    
    def download_daily_symbols(self) -> Dict[str, bool]:
        """Download NSE_CM.csv and NSE_FO.csv with backup."""
        logger.info("Starting daily symbol download")
        return self.downloader.download_daily_symbols()
    
    def _parse_csv_file(self, file_path: Path, columns: List[str]) -> pd.DataFrame:
        """Parse CSV file without headers."""
        try:
            df = pd.read_csv(file_path, header=None, names=columns)
            logger.info(f"Parsed {len(df)} rows from {file_path}")
            return df
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
            return pd.DataFrame()
    
    def _create_tables_if_not_exist(self) -> bool:
        """Create NSE raw data tables if they don't exist."""
        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor() as cursor:
                    # Read and execute schema
                    schema_path = Path("src/database/schemas.sql")
                    if schema_path.exists():
                        with open(schema_path, 'r') as f:
                            schema_sql = f.read()

                        # Execute the entire schema as one block to handle PL/pgSQL
                        try:
                            cursor.execute(schema_sql)
                        except Exception as schema_error:
                            # If schema execution fails, rollback and try to create just the NSE tables
                            logger.warning(f"Full schema execution failed: {schema_error}")
                            logger.info("Attempting to create NSE tables only...")

                            # Rollback the failed transaction
                            conn.rollback()

                            # Start a new transaction and create just the NSE raw tables
                            with conn.cursor() as new_cursor:
                                # Create just the NSE raw tables
                                nse_tables_sql = """
                                -- Create NSE_CM Raw Data Table (Cash Market - Equities and Indices)
                                CREATE TABLE IF NOT EXISTS nse_cm_raw (
                                    id SERIAL PRIMARY KEY,
                                    fytoken VARCHAR(50) NOT NULL,
                                    company_name VARCHAR(200),
                                    segment INTEGER,
                                    lot_size INTEGER,
                                    tick_size DECIMAL(10,4),
                                    isin VARCHAR(20),
                                    trading_hours VARCHAR(50),
                                    last_update_date DATE,
                                    expiry_timestamp BIGINT,
                                    symbol_name VARCHAR(100),
                                    exchange_segment INTEGER,
                                    exchange_instrument_id INTEGER,
                                    instrument_id INTEGER,
                                    symbol VARCHAR(100),
                                    token INTEGER,
                                    minimum_lot_size DECIMAL(12,4),
                                    instrument_type VARCHAR(10),
                                    underlying_fytoken VARCHAR(50),
                                    underlying_symbol VARCHAR(100),
                                    option_type INTEGER,
                                    strike_price DECIMAL(12,4),
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
                                );

                                -- Create NSE_FO Raw Data Table (Futures and Options)
                                CREATE TABLE IF NOT EXISTS nse_fo_raw (
                                    id SERIAL PRIMARY KEY,
                                    fytoken VARCHAR(50) NOT NULL,
                                    company_name VARCHAR(200),
                                    segment INTEGER,
                                    lot_size INTEGER,
                                    tick_size DECIMAL(10,4),
                                    isin VARCHAR(20),
                                    trading_hours VARCHAR(50),
                                    last_update_date DATE,
                                    expiry_timestamp BIGINT,
                                    symbol_name VARCHAR(100),
                                    exchange_segment INTEGER,
                                    exchange_instrument_id INTEGER,
                                    instrument_id INTEGER,
                                    symbol VARCHAR(100),
                                    token INTEGER,
                                    minimum_lot_size DECIMAL(12,4),
                                    instrument_type VARCHAR(10),
                                    underlying_fytoken VARCHAR(50),
                                    underlying_symbol VARCHAR(100),
                                    option_type VARCHAR(10),
                                    strike_price DECIMAL(12,4),
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
                                );
                                """
                                new_cursor.execute(nse_tables_sql)

                        conn.commit()
                        logger.info("Database tables created/verified successfully")
                        return True
                    else:
                        logger.error("Schema file not found")
                        return False
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def _load_data_to_raw_table(self, df: pd.DataFrame, table_name: str) -> bool:
        """Load DataFrame to raw NSE table."""
        if df.empty:
            logger.warning(f"No data to load into {table_name}")
            return False

        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor() as cursor:
                    # Clear existing data
                    cursor.execute(f"DELETE FROM {table_name}")

                    # Prepare insert statement
                    columns = list(df.columns)
                    placeholders = ', '.join(['%s'] * len(columns))
                    insert_sql = f"""
                        INSERT INTO {table_name} ({', '.join(columns)})
                        VALUES ({placeholders})
                    """

                    # Insert data in batches for better performance
                    batch_size = 1000
                    for i in range(0, len(df), batch_size):
                        batch = df.iloc[i:i+batch_size]
                        for _, row in batch.iterrows():
                            cursor.execute(insert_sql, tuple(row))

                    conn.commit()
                    logger.info(f"Loaded {len(df)} rows into {table_name}")
                    return True
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error loading data to {table_name}: {e}")
            return False
    
    def _extract_symbol_info(self, symbol: str) -> Dict[str, any]:
        """Extract market type and other info from symbol."""
        symbol_info = {
            'market_type': None,
            'underlying': None,
            'expiry_date': None,
            'strike_price': None,
            'option_type': None
        }
        
        # Check EQUITY pattern
        if self.patterns['EQUITY'].match(symbol):
            symbol_info['market_type'] = 'EQUITY'
            symbol_info['underlying'] = symbol.replace('-EQ', '')
        
        # Check INDEX pattern
        elif self.patterns['INDEX'].match(symbol):
            symbol_info['market_type'] = 'INDEX'
            symbol_info['underlying'] = symbol.replace('-INDEX', '')
        
        # Check FUTURES pattern
        elif self.patterns['FUTURES'].match(symbol):
            symbol_info['market_type'] = 'FUTURES'
            # Extract underlying and expiry from UNDERLYINGYYMMMFUT
            match = re.match(r'^([A-Z0-9]+)(\d{2})([A-Z]{3})FUT$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_str = match.group(3)
                symbol_info['expiry_date'] = self._parse_expiry_date(year, month_str)
        
        # Check OPTIONS patterns
        elif self.patterns['OPTIONS_MONTHLY'].match(symbol):
            symbol_info['market_type'] = 'OPTIONS'
            # Extract from UNDERLYINGYYMMMSTRIKECE/PE
            match = re.match(r'^([A-Z0-9]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_str = match.group(3)
                symbol_info['expiry_date'] = self._parse_expiry_date(year, month_str)
                symbol_info['strike_price'] = float(match.group(4))
                symbol_info['option_type'] = match.group(5)
        
        elif self.patterns['OPTIONS_WEEKLY'].match(symbol):
            symbol_info['market_type'] = 'OPTIONS'
            # Extract from UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE)
            match = re.match(r'^([A-Z0-9]+)(\d{2})(\d)(\d{2})(\d+(?:\.\d+)?)(CE|PE)$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_digit = match.group(3)
                day = int(match.group(4))
                symbol_info['expiry_date'] = self._parse_weekly_expiry_date(year, month_digit, day)
                symbol_info['strike_price'] = float(match.group(5))
                symbol_info['option_type'] = match.group(6)
        
        return symbol_info
    
    def _parse_expiry_date(self, year: int, month_str: str) -> Optional[datetime]:
        """Parse expiry date from year and month string."""
        month_map = {
            'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
            'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
        }
        
        try:
            month = month_map.get(month_str)
            if month:
                # Last Thursday of the month (typical expiry)
                last_day = datetime(year, month + 1, 1) - timedelta(days=1) if month < 12 else datetime(year, 12, 31)
                while last_day.weekday() != 3:  # Thursday is 3
                    last_day -= timedelta(days=1)
                return last_day
        except Exception as e:
            logger.warning(f"Error parsing expiry date {year}-{month_str}: {e}")
        
        return None
    
    def _parse_weekly_expiry_date(self, year: int, month_digit: str, day: int) -> Optional[datetime]:
        """Parse weekly expiry date from year, month digit, and day."""
        # Month digit mapping (1-9, O, N, D for Oct, Nov, Dec)
        month_map = {
            '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6,
            '7': 7, '8': 8, '9': 9, 'O': 10, 'N': 11, 'D': 12
        }
        
        try:
            month = month_map.get(month_digit)
            if month:
                return datetime(year, month, day)
        except Exception as e:
            logger.warning(f"Error parsing weekly expiry date {year}-{month_digit}-{day}: {e}")
        
        return None

    def _filter_relevant_symbols(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter symbols based on pattern matching criteria."""
        if df.empty:
            return df

        filtered_rows = []

        for _, row in df.iterrows():
            symbol = row.get('symbol_name', '').split(':')[-1] if ':' in str(row.get('symbol_name', '')) else str(row.get('symbol_name', ''))

            symbol_info = self._extract_symbol_info(symbol)

            if symbol_info['market_type']:
                # Add symbol info to row
                row_dict = row.to_dict()
                row_dict.update(symbol_info)
                filtered_rows.append(row_dict)

        if filtered_rows:
            filtered_df = pd.DataFrame(filtered_rows)
            logger.info(f"Filtered {len(filtered_df)} relevant symbols from {len(df)} total symbols")
            return filtered_df
        else:
            logger.warning("No relevant symbols found after filtering")
            return pd.DataFrame()

    def _update_symbol_mapping_table(self, df: pd.DataFrame) -> bool:
        """Update symbol_mapping table with filtered symbols."""
        if df.empty:
            logger.warning("No data to update in symbol_mapping table")
            return False

        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor() as cursor:
                    # Clear existing data for today
                    cursor.execute("DELETE FROM symbol_mapping WHERE DATE(created_at) = CURRENT_DATE")

                    # Insert new symbols
                    insert_sql = """
                        INSERT INTO symbol_mapping (
                            nse_symbol, fyers_symbol, market_type, exchange,
                            expiry_date, strike_price, option_type, is_active
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """

                    for _, row in df.iterrows():
                        nse_symbol = row.get('symbol_name', '').split(':')[-1] if ':' in str(row.get('symbol_name', '')) else str(row.get('symbol_name', ''))
                        fyers_symbol = row.get('symbol_name', '')
                        market_type = row.get('market_type')
                        expiry_date = row.get('expiry_date')
                        strike_price = row.get('strike_price')
                        option_type = row.get('option_type')

                        cursor.execute(insert_sql, (
                            nse_symbol, fyers_symbol, market_type, 'NSE',
                            expiry_date, strike_price, option_type, True
                        ))

                    conn.commit()
                    logger.info(f"Updated symbol_mapping table with {len(df)} symbols")
                    return True
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error updating symbol_mapping table: {e}")
            return False

    def process_nse_files(self) -> Dict[str, bool]:
        """Main method to process NSE files - download, parse, filter, and update DB."""
        results = {
            'download': False,
            'nse_cm_processed': False,
            'nse_fo_processed': False,
            'symbol_mapping_updated': False,
            'tables_created': False
        }

        try:
            # Step 1: Create tables if needed
            results['tables_created'] = self._create_tables_if_not_exist()
            if not results['tables_created']:
                logger.error("Failed to create/verify database tables")
                return results

            # Step 2: Download files
            download_results = self.download_daily_symbols()
            results['download'] = all(download_results.values())

            if not results['download']:
                logger.error("Failed to download NSE files")
                return results

            # Step 3: Process NSE_CM.csv
            nse_cm_path = Path("NSE_CM.csv")
            if nse_cm_path.exists():
                nse_cm_df = self._parse_csv_file(nse_cm_path, self.nse_cm_columns)
                if not nse_cm_df.empty:
                    results['nse_cm_processed'] = self._load_data_to_raw_table(nse_cm_df, 'nse_cm_raw')

            # Step 4: Process NSE_FO.csv
            nse_fo_path = Path("NSE_FO.csv")
            if nse_fo_path.exists():
                nse_fo_df = self._parse_csv_file(nse_fo_path, self.nse_fo_columns)
                if not nse_fo_df.empty:
                    results['nse_fo_processed'] = self._load_data_to_raw_table(nse_fo_df, 'nse_fo_raw')

            # Step 5: Filter and update symbol mapping
            all_symbols = []

            if results['nse_cm_processed'] and not nse_cm_df.empty:
                filtered_cm = self._filter_relevant_symbols(nse_cm_df)
                if not filtered_cm.empty:
                    all_symbols.append(filtered_cm)

            if results['nse_fo_processed'] and not nse_fo_df.empty:
                filtered_fo = self._filter_relevant_symbols(nse_fo_df)
                if not filtered_fo.empty:
                    all_symbols.append(filtered_fo)

            if all_symbols:
                combined_symbols = pd.concat(all_symbols, ignore_index=True)
                results['symbol_mapping_updated'] = self._update_symbol_mapping_table(combined_symbols)

            # Step 6: Cleanup old backups
            self.downloader.cleanup_old_backups()

            logger.info(f"NSE processing completed. Results: {results}")
            return results

        except Exception as e:
            logger.error(f"Error in process_nse_files: {e}")
            return results

    def get_sample_symbols_by_type(self) -> Dict[str, str]:
        """Get one sample symbol from each market type for testing."""
        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    sample_symbols = {}

                    for market_type in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
                        cursor.execute("""
                            SELECT nse_symbol, fyers_symbol
                            FROM symbol_mapping
                            WHERE market_type = %s
                            AND is_active = TRUE
                            LIMIT 1
                        """, (market_type,))

                        result = cursor.fetchone()
                        if result:
                            sample_symbols[market_type] = result['fyers_symbol']

                    return sample_symbols
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error getting sample symbols: {e}")
            return {}

    def validate_data_integrity(self) -> Dict[str, any]:
        """Validate data integrity across NSE tables."""
        integrity_report = {
            'nse_cm_count': 0,
            'nse_fo_count': 0,
            'symbol_mapping_count': 0,
            'equity_count': 0,
            'index_count': 0,
            'futures_count': 0,
            'options_count': 0,
            'missing_data': [],
            'validation_passed': False
        }

        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    # Count records in each table
                    cursor.execute("SELECT COUNT(*) as count FROM nse_cm_raw")
                    integrity_report['nse_cm_count'] = cursor.fetchone()['count']

                    cursor.execute("SELECT COUNT(*) as count FROM nse_fo_raw")
                    integrity_report['nse_fo_count'] = cursor.fetchone()['count']

                    cursor.execute("SELECT COUNT(*) as count FROM symbol_mapping")
                    integrity_report['symbol_mapping_count'] = cursor.fetchone()['count']

                    # Count by market type
                    for market_type in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
                        cursor.execute("""
                            SELECT COUNT(*) as count
                            FROM symbol_mapping
                            WHERE market_type = %s AND is_active = TRUE
                        """, (market_type,))

                        count = cursor.fetchone()['count']
                        integrity_report[f'{market_type.lower()}_count'] = count

                        if count == 0:
                            integrity_report['missing_data'].append(f"No {market_type} symbols found")

                    # Basic validation
                    integrity_report['validation_passed'] = (
                        integrity_report['nse_cm_count'] > 0 and
                        integrity_report['nse_fo_count'] > 0 and
                        integrity_report['symbol_mapping_count'] > 0 and
                        len(integrity_report['missing_data']) == 0
                    )

                    logger.info(f"Data integrity validation: {integrity_report}")
                    return integrity_report
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error validating data integrity: {e}")
            integrity_report['missing_data'].append(f"Database error: {e}")
            return integrity_report
